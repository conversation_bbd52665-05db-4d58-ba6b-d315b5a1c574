#!/usr/bin/env python3
"""
测试合同包字段清理脚本

该脚本用于验证：
1. contains_contract_package_keyword 函数是否正确工作
2. 文档筛选逻辑是否正确
3. 字段清理逻辑是否按预期工作
"""

import sys
import os

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from clean_contract_package_fields import (
    contains_contract_package_keyword,
    CONTRACT_PACKAGE_KEYWORD,
    CONTRACT_FIELDS_TO_CLEAR
)
from utils.log_cfg import log


def test_contract_package_keyword_detection():
    """测试合同包关键词检测函数"""
    log.info("测试合同包关键词检测函数...")
    
    test_cases = [
        # (text, expected_result, description)
        ("投标合同包.pdf", True, "包含'合同包'关键词"),
        ("2023年合同包资料.zip", True, "包含'合同包'关键词"),
        ("合同包", True, "仅包含'合同包'关键词"),
        ("医疗设备采购合同包文件", True, "包含'合同包'关键词（中间位置）"),
        ("合同文件.pdf", False, "不包含'合同包'关键词"),
        ("服务协议.docx", False, "不包含'合同包'关键词"),
        ("招标文件", False, "不包含'合同包'关键词"),
        ("", False, "空内容"),
        (None, False, "None内容"),
        ("合同 包", False, "关键词被分割"),
        ("合同-包", False, "关键词被分割"),
        ("这是合同包装文件", True, "包含'合同包'关键词（作为词汇的一部分）"),
    ]
    
    success_count = 0
    for i, (text, expected, description) in enumerate(test_cases, 1):
        try:
            result = contains_contract_package_keyword(text)
            
            if result == expected:
                log.info(f"  ✓ 测试 {i}: {description} -> {result}")
                success_count += 1
            else:
                log.error(f"  ✗ 测试 {i}: {description} -> 期望 {expected}, 实际 {result}")
                log.error(f"    内容: '{text}'")
        except Exception as e:
            log.error(f"  ✗ 测试 {i}: {description} -> 异常: {e}")
    
    log.info(f"合同包关键词检测函数测试完成: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_constants():
    """测试常量定义"""
    log.info("测试常量定义...")
    
    # 测试关键词常量
    if CONTRACT_PACKAGE_KEYWORD == "合同包":
        log.info(f"✓ CONTRACT_PACKAGE_KEYWORD 常量正确: '{CONTRACT_PACKAGE_KEYWORD}'")
        keyword_test_passed = True
    else:
        log.error(f"✗ CONTRACT_PACKAGE_KEYWORD 常量错误: 期望 '合同包', 实际 '{CONTRACT_PACKAGE_KEYWORD}'")
        keyword_test_passed = False
    
    # 测试字段常量
    expected_fields = ["contract_name", "contract_ext", "contract_link_out", "contract_link_key"]
    if CONTRACT_FIELDS_TO_CLEAR == expected_fields:
        log.info(f"✓ CONTRACT_FIELDS_TO_CLEAR 常量正确: {CONTRACT_FIELDS_TO_CLEAR}")
        fields_test_passed = True
    else:
        log.error(f"✗ CONTRACT_FIELDS_TO_CLEAR 常量错误: 期望 {expected_fields}, 实际 {CONTRACT_FIELDS_TO_CLEAR}")
        fields_test_passed = False
    
    return keyword_test_passed and fields_test_passed


def test_document_processing_logic():
    """测试文档处理逻辑"""
    log.info("测试文档处理逻辑...")
    
    # 模拟文档数据
    test_documents = [
        {
            "doc_id": "doc1",
            "contract_name": "投标合同包.pdf",
            "contract_ext": ".pdf",
            "contract_link_out": "http://example.com/file1.pdf",
            "contract_link_key": "key123",
            "should_clean": True,
            "description": "包含'合同包'关键词，应该被清理"
        },
        {
            "doc_id": "doc2", 
            "contract_name": "2023年合同包资料.zip",
            "contract_ext": ".zip",
            "contract_link_out": "http://example.com/file2.zip",
            "contract_link_key": "key456",
            "should_clean": True,
            "description": "包含'合同包'关键词，应该被清理"
        },
        {
            "doc_id": "doc3",
            "contract_name": "服务合同.pdf",
            "contract_ext": ".pdf", 
            "contract_link_out": "http://example.com/file3.pdf",
            "contract_link_key": "key789",
            "should_clean": False,
            "description": "不包含'合同包'关键词，不应该被清理"
        },
        {
            "doc_id": "doc4",
            "contract_name": "医疗设备采购合同包文件.docx",
            "contract_ext": ".docx",
            "contract_link_out": "http://example.com/file4.docx", 
            "contract_link_key": "key101",
            "should_clean": True,
            "description": "包含'合同包'关键词，应该被清理"
        }
    ]
    
    success_count = 0
    for i, doc in enumerate(test_documents, 1):
        contract_name = doc["contract_name"]
        should_clean = doc["should_clean"]
        description = doc["description"]
        
        # 测试关键词检测
        detected = contains_contract_package_keyword(contract_name)
        
        if detected == should_clean:
            log.info(f"  ✓ 测试 {i}: {description}")
            log.info(f"    文档ID: {doc['doc_id']}")
            log.info(f"    合同名称: '{contract_name}'")
            log.info(f"    检测结果: {detected} (期望: {should_clean})")
            
            if should_clean:
                log.info(f"    将清理字段: {CONTRACT_FIELDS_TO_CLEAR}")
            else:
                log.info(f"    保持字段不变")
            
            success_count += 1
        else:
            log.error(f"  ✗ 测试 {i}: {description}")
            log.error(f"    文档ID: {doc['doc_id']}")
            log.error(f"    合同名称: '{contract_name}'")
            log.error(f"    检测结果: {detected}, 期望: {should_clean}")
        
        log.info("-" * 40)
    
    log.info(f"文档处理逻辑测试完成: {success_count}/{len(test_documents)} 通过")
    return success_count == len(test_documents)


def test_field_clearing_logic():
    """测试字段清理逻辑"""
    log.info("测试字段清理逻辑...")
    
    # 模拟需要清理的文档
    test_doc = {
        "doc_id": "test_doc",
        "contract_name": "投标合同包.pdf",
        "contract_ext": ".pdf",
        "contract_link_out": "http://example.com/contract.pdf",
        "contract_link_key": "contract_key_123"
    }
    
    log.info("模拟文档清理过程:")
    log.info(f"文档ID: {test_doc['doc_id']}")
    log.info("清理前字段值:")
    for field in CONTRACT_FIELDS_TO_CLEAR:
        value = test_doc.get(field)
        log.info(f"  {field}: '{value}'")
    
    # 模拟清理过程
    if contains_contract_package_keyword(test_doc["contract_name"]):
        log.info("\n检测到'合同包'关键词，执行字段清理...")
        
        # 模拟清理后的字段值
        cleared_fields = {}
        for field in CONTRACT_FIELDS_TO_CLEAR:
            cleared_fields[field] = None
        
        log.info("清理后字段值:")
        for field, value in cleared_fields.items():
            log.info(f"  {field}: {value}")
        
        log.info("✓ 字段清理逻辑正确")
        return True
    else:
        log.error("✗ 字段清理逻辑错误：未检测到'合同包'关键词")
        return False


def demo_batch_processing():
    """演示批量处理逻辑"""
    log.info("演示批量处理逻辑...")
    
    # 模拟一批文档
    batch_documents = [
        {"doc_id": "batch_1", "contract_name": "合同包1.pdf"},
        {"doc_id": "batch_2", "contract_name": "正常合同.pdf"},
        {"doc_id": "batch_3", "contract_name": "投标合同包.docx"},
        {"doc_id": "batch_4", "contract_name": "服务协议.pdf"},
        {"doc_id": "batch_5", "contract_name": "2023年合同包资料.zip"},
    ]
    
    log.info(f"批次文档总数: {len(batch_documents)}")
    
    need_cleaning = []
    keep_unchanged = []
    
    for doc in batch_documents:
        if contains_contract_package_keyword(doc["contract_name"]):
            need_cleaning.append(doc)
        else:
            keep_unchanged.append(doc)
    
    log.info(f"需要清理的文档: {len(need_cleaning)}")
    for doc in need_cleaning:
        log.info(f"  - {doc['doc_id']}: '{doc['contract_name']}'")
    
    log.info(f"保持不变的文档: {len(keep_unchanged)}")
    for doc in keep_unchanged:
        log.info(f"  - {doc['doc_id']}: '{doc['contract_name']}'")
    
    # 计算清理比例
    cleaning_ratio = len(need_cleaning) / len(batch_documents) * 100
    log.info(f"清理比例: {cleaning_ratio:.1f}%")
    
    return True


def main():
    """主函数"""
    try:
        log.info("开始测试合同包字段清理功能...")
        log.info("=" * 60)

        all_tests_passed = True

        # 1. 测试常量定义
        if not test_constants():
            all_tests_passed = False

        log.info("=" * 60)

        # 2. 测试关键词检测函数
        if not test_contract_package_keyword_detection():
            all_tests_passed = False

        log.info("=" * 60)

        # 3. 测试文档处理逻辑
        if not test_document_processing_logic():
            all_tests_passed = False

        log.info("=" * 60)

        # 4. 测试字段清理逻辑
        if not test_field_clearing_logic():
            all_tests_passed = False

        log.info("=" * 60)

        # 5. 演示批量处理逻辑
        if not demo_batch_processing():
            all_tests_passed = False

        log.info("=" * 60)

        if all_tests_passed:
            log.info("✓ 所有测试通过！合同包字段清理功能正常工作。")
        else:
            log.error("✗ 部分测试失败，请检查代码实现。")
            sys.exit(1)

    except Exception as e:
        log.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
