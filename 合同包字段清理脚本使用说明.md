# 合同包字段清理脚本使用说明

## 🎯 功能概述

`clean_contract_package_fields.py` 脚本用于批量清理 Elasticsearch 中包含"合同包"关键词的合同文件字段。当 `contract_name` 字段包含"合同包"关键词时，将以下4个字段全部设置为 null：

- `contract_name`
- `contract_ext`
- `contract_link_out`
- `contract_link_key`

## 📋 主要特性

### 1. 安全机制
- **默认启用 dry-run 模式**：防止意外修改数据
- **用户确认机制**：执行实际修改前需要用户确认
- **批量处理**：支持大量数据的分批处理
- **详细日志**：记录所有操作过程和结果

### 2. 灵活配置
- 支持自定义索引名称
- 可调整批处理大小
- 支持处理单批次或所有文档
- 提供详细的统计信息

### 3. 错误处理
- 完善的异常处理机制
- 批量操作失败时的错误统计
- 操作中断时的安全退出

## 🚀 使用方法

### 基本用法

```bash
# 1. 试运行模式（默认，安全预览）
python clean_contract_package_fields.py

# 2. 指定索引的试运行
python clean_contract_package_fields.py --index markersweb_attachment_analysis_v3

# 3. 执行实际清理操作
python clean_contract_package_fields.py --execute

# 4. 处理所有需要清理的文档
python clean_contract_package_fields.py --all --execute
```

### 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--index` | 目标索引名称 | `markersweb_attachment_analysis_v3` |
| `--batch-size` | 批处理大小 | `100` |
| `--dry-run` | 试运行模式（默认启用） | `True` |
| `--execute` | 执行实际更新操作 | `False` |
| `--all` | 处理所有需要清理的文档 | `False` |

### 使用示例

#### 示例1：安全预览
```bash
# 查看将要清理的文档，不执行实际修改
python clean_contract_package_fields.py --index markersweb_attachment_analysis_v3
```

#### 示例2：小批量测试
```bash
# 执行实际清理，但只处理前100个文档
python clean_contract_package_fields.py --execute --batch-size 100
```

#### 示例3：全量清理
```bash
# 清理所有包含"合同包"关键词的文档
python clean_contract_package_fields.py --all --execute
```

## 📊 输出信息

### 统计信息
脚本会输出详细的统计信息：

```
处理统计:
  本批次文档总数: 150
  需要清理的文档: 45
  清理字段: ['contract_name', 'contract_ext', 'contract_link_out', 'contract_link_key']
  清理原因: 包含'合同包'关键词
```

### 修改前后对比
对于每个被清理的文档，会显示修改前后的字段值：

```
[1/45] 清理合同包字段 doc_123:
  修改前 - contract_name: '投标合同包.pdf'
  修改前 - contract_ext: '.pdf'
  修改前 - contract_link_out: 'http://example.com/file.pdf'
  修改前 - contract_link_key: 'key_123'
  修改后 - 所有合同字段: null
```

## 🔧 清理规则

### 触发条件
- `contract_name` 字段存在且不为空
- `contract_name` 字段包含"合同包"关键词

### 清理操作
当满足触发条件时，将以下字段设置为 `null`：
1. `contract_name` - 合同文件名称
2. `contract_ext` - 合同文件扩展名
3. `contract_link_out` - 合同文件外部链接
4. `contract_link_key` - 合同文件链接键

### 匹配示例
以下文件名会被识别为包含"合同包"关键词：
- ✅ `投标合同包.pdf`
- ✅ `2023年合同包资料.zip`
- ✅ `医疗设备采购合同包文件.docx`
- ✅ `合同包`
- ❌ `合同文件.pdf`
- ❌ `服务协议.docx`
- ❌ `合同 包` (被分割)

## 🛡️ 安全措施

### 1. 默认安全模式
- 脚本默认以 `--dry-run` 模式运行
- 必须明确指定 `--execute` 才会执行实际修改

### 2. 用户确认
执行实际修改前会显示确认提示：
```
即将修改 45 个文档的合同字段！
确认执行实际修改操作？(输入 'YES' 确认):
```

### 3. 操作记录
- 所有操作都有详细的日志记录
- 支持通过日志追踪修改历史
- 错误信息会被完整记录

## 🧪 测试验证

### 运行测试脚本
```bash
# 运行完整的功能测试
python test_clean_contract_package.py
```

测试脚本会验证：
- 关键词检测函数的正确性
- 文档筛选逻辑的准确性
- 字段清理逻辑的完整性
- 批量处理逻辑的有效性

### 测试结果示例
```
✓ 所有测试通过！合同包字段清理功能正常工作。
```

## ⚠️ 注意事项

### 1. 环境要求
- 确保 `.env` 文件包含正确的 Elasticsearch 配置
- 需要有目标索引的读写权限

### 2. 数据备份
- 建议在执行实际清理前备份重要数据
- 可以先在测试环境验证脚本功能

### 3. 性能考虑
- 大量数据处理时建议使用 `--all` 参数进行分批处理
- 可以根据系统性能调整 `--batch-size` 参数

### 4. 监控建议
- 关注脚本执行日志
- 监控 Elasticsearch 集群性能
- 验证清理结果的正确性

## 🔍 故障排除

### 常见问题

1. **索引不存在**
   ```
   错误: 索引 markersweb_attachment_analysis_v3 不存在
   解决: 检查索引名称是否正确
   ```

2. **连接失败**
   ```
   错误: 初始化ES客户端失败
   解决: 检查 .env 文件中的 ES 配置
   ```

3. **权限不足**
   ```
   错误: 批量更新文档失败
   解决: 确认用户有索引的写权限
   ```

### 日志级别
- `INFO`: 正常操作信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `DEBUG`: 详细调试信息

## 📈 性能优化

### 批处理大小建议
- 小型集群：50-100
- 中型集群：100-500
- 大型集群：500-1000

### 监控指标
- 处理速度（文档/秒）
- 内存使用情况
- Elasticsearch 响应时间
- 错误率

## 🔄 版本历史

- **v1.0**: 初始版本，支持基本的合同包字段清理功能
- 支持 dry-run 模式和批量处理
- 包含完整的测试套件和使用文档
