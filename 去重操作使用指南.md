# chn_ylcg索引去重操作使用指南

## 快速开始

### 1. 安全执行流程（推荐）

使用提供的安全执行脚本，它会引导你完成整个去重流程：

```bash
python run_deduplication_example.py
```

该脚本会按顺序执行：
1. 检查Elasticsearch连接
2. 运行测试分析
3. 执行试运行模式
4. 确认后执行实际去重
5. 验证去重结果

### 2. 手动执行步骤

如果你想手动控制每个步骤：

#### 步骤1: 测试分析
```bash
python test_deduplicate_chn_ylcg.py
```

#### 步骤2: 试运行
```bash
python deduplicate_chn_ylcg.py --dry-run
```

#### 步骤3: 实际执行
```bash
python deduplicate_chn_ylcg.py
```

## 去重规则说明

### 去重标识符
- 使用 `url` 字段作为去重标识符
- 相同url的文档被视为重复

### 保留优先级
1. **appendix字段优先级**：
   - 优先保留 `appendix` 字段为非空数组的文档
   - `appendix` 为 `null`、`[]` 或非数组类型的文档优先级较低

2. **时间优先级**：
   - 当appendix字段状态相同时，保留文档ID最大的（最新的）文档

### 示例

```
URL: "https://example.com/tender/123"

文档A: _id="abc123", appendix=null
文档B: _id="def456", appendix=[]  
文档C: _id="ghi789", appendix=[{"name": "招标文件.pdf"}]

结果: 保留文档C，删除文档A和B
```

## 输出文件

### 备份文件
- 文件名格式：`deleted_documents_backup_YYYYMMDD_HHMMSS.csv`
- 包含所有被删除文档的详细信息
- 可用于数据恢复或审计

### 日志文件
- 详细记录整个去重过程
- 包括统计信息、错误信息等

## 安全措施

### 执行前检查
- [ ] 确认Elasticsearch连接正常
- [ ] 确认目标索引存在且可访问
- [ ] 运行测试分析了解数据情况
- [ ] 执行试运行模式预览操作结果

### 数据保护
- [ ] 创建索引快照备份（推荐）
- [ ] 脚本自动备份删除的文档信息
- [ ] 支持试运行模式预览操作
- [ ] 详细的操作日志记录

### 执行后验证
- [ ] 检查删除统计信息
- [ ] 验证数据一致性
- [ ] 运行应用程序测试
- [ ] 保存备份文件

## 常见问题

### Q: 如何恢复被误删的文档？
A: 使用备份CSV文件中的信息，可以重新插入被删除的文档。

### Q: 去重操作可以中断吗？
A: 可以，使用Ctrl+C中断。已删除的文档不会自动恢复，需要从备份恢复。

### Q: 如何处理大量数据？
A: 调整 `--batch-size` 参数，默认100，可以根据系统性能调整。

### Q: 去重操作失败怎么办？
A: 检查日志文件，确认Elasticsearch状态，必要时从备份恢复数据。

## 性能建议

### 批处理大小
- 默认：100个文档/批次
- 高性能环境：可增加到200-500
- 低性能环境：可减少到50

### 执行时间
- 取决于重复文档数量和系统性能
- 建议在业务低峰期执行

## 监控指标

执行过程中关注以下指标：
- 索引文档总数
- 重复url数量
- 计划删除文档数
- 实际删除成功数
- 删除失败数
- 数据一致性验证结果

## 联系支持

如果遇到问题：
1. 查看详细日志
2. 检查备份文件
3. 验证Elasticsearch状态
4. 联系技术支持团队
