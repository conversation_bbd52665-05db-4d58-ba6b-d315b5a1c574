#!/usr/bin/env python3
"""
调试合同包查询脚本

用于调试为什么 clean_contract_package_fields.py 没有找到包含"合同包"关键词的文档
"""

import sys
import os
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from es_deal import init_es_client, search_documents
from utils.log_cfg import log


def check_index_exists(es_client, index_name: str):
    """检查索引是否存在"""
    try:
        exists = es_client.indices.exists(index=index_name)
        log.info(f"索引 {index_name} 存在: {exists}")
        return exists
    except Exception as e:
        log.error(f"检查索引存在性失败: {e}")
        return False


def get_index_stats(es_client, index_name: str):
    """获取索引统计信息"""
    try:
        stats = es_client.indices.stats(index=index_name)
        total_docs = stats['indices'][index_name]['total']['docs']['count']
        log.info(f"索引 {index_name} 总文档数: {total_docs}")
        return total_docs
    except Exception as e:
        log.error(f"获取索引统计失败: {e}")
        return 0


def check_contract_name_field(es_client, index_name: str):
    """检查contract_name字段的情况"""
    try:
        # 查询有contract_name字段的文档数量
        query = {
            "query": {
                "exists": {"field": "contract_name"}
            }
        }
        
        result = es_client.count(index=index_name, body=query)
        count = result.get("count", 0)
        log.info(f"包含 contract_name 字段的文档数: {count}")
        
        return count
    except Exception as e:
        log.error(f"检查contract_name字段失败: {e}")
        return 0


def sample_contract_names(es_client, index_name: str, size: int = 20):
    """获取contract_name字段的样本数据"""
    try:
        query = {
            "query": {
                "exists": {"field": "contract_name"}
            },
            "_source": ["contract_name"],
            "size": size
        }
        
        result = search_documents(es_client, index_name, query=query)
        hits = result.get("hits", {}).get("hits", [])
        
        log.info(f"contract_name 字段样本数据 (前{len(hits)}个):")
        for i, hit in enumerate(hits, 1):
            contract_name = hit["_source"].get("contract_name")
            log.info(f"  {i}. '{contract_name}'")
            
        return hits
    except Exception as e:
        log.error(f"获取样本数据失败: {e}")
        return []


def test_different_queries(es_client, index_name: str):
    """测试不同的查询方式"""
    log.info("测试不同的查询方式...")
    
    # 测试查询列表
    test_queries = [
        {
            "name": "wildcard查询: *合同包*",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "contract_name"}},
                            {"wildcard": {"contract_name": "*合同包*"}}
                        ]
                    }
                }
            }
        },
        {
            "name": "match查询: 合同包",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "contract_name"}},
                            {"match": {"contract_name": "合同包"}}
                        ]
                    }
                }
            }
        },
        {
            "name": "term查询: 合同包",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "contract_name"}},
                            {"term": {"contract_name": "合同包"}}
                        ]
                    }
                }
            }
        },
        {
            "name": "match_phrase查询: 合同包",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "contract_name"}},
                            {"match_phrase": {"contract_name": "合同包"}}
                        ]
                    }
                }
            }
        },
        {
            "name": "regexp查询: .*合同包.*",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "contract_name"}},
                            {"regexp": {"contract_name": ".*合同包.*"}}
                        ]
                    }
                }
            }
        },
        {
            "name": "query_string查询: *合同包*",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "contract_name"}},
                            {"query_string": {"default_field": "contract_name", "query": "*合同包*"}}
                        ]
                    }
                }
            }
        }
    ]
    
    for test in test_queries:
        try:
            result = es_client.count(index=index_name, body=test["query"])
            count = result.get("count", 0)
            log.info(f"  {test['name']}: {count} 个文档")
            
            # 如果找到文档，显示几个样本
            if count > 0:
                sample_query = test["query"].copy()
                sample_query["size"] = 3
                sample_query["_source"] = ["contract_name"]
                
                sample_result = search_documents(es_client, index_name, query=sample_query)
                sample_hits = sample_result.get("hits", {}).get("hits", [])
                
                log.info(f"    样本数据:")
                for i, hit in enumerate(sample_hits, 1):
                    contract_name = hit["_source"].get("contract_name")
                    log.info(f"      {i}. '{contract_name}'")
                    
        except Exception as e:
            log.error(f"  {test['name']}: 查询失败 - {e}")


def search_specific_patterns(es_client, index_name: str):
    """搜索特定的模式"""
    log.info("搜索特定的合同包模式...")
    
    patterns = [
        "合同包",
        "投标合同包", 
        "采购合同包",
        "医疗合同包",
        "设备合同包"
    ]
    
    for pattern in patterns:
        try:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {"exists": {"field": "contract_name"}},
                            {"wildcard": {"contract_name": f"*{pattern}*"}}
                        ]
                    }
                },
                "_source": ["contract_name"],
                "size": 5
            }
            
            result = search_documents(es_client, index_name, query=query)
            hits = result.get("hits", {}).get("hits", [])
            total = result.get("hits", {}).get("total", {}).get("value", 0)
            
            log.info(f"  模式 '{pattern}': {total} 个文档")
            for i, hit in enumerate(hits, 1):
                contract_name = hit["_source"].get("contract_name")
                log.info(f"    {i}. '{contract_name}'")
                
        except Exception as e:
            log.error(f"  搜索模式 '{pattern}' 失败: {e}")


def check_field_mapping(es_client, index_name: str):
    """检查字段映射"""
    try:
        mapping = es_client.indices.get_mapping(index=index_name)
        
        if index_name in mapping:
            properties = mapping[index_name].get("mappings", {}).get("properties", {})
            
            if "contract_name" in properties:
                field_info = properties["contract_name"]
                log.info(f"contract_name 字段映射: {field_info}")
            else:
                log.warning("contract_name 字段不存在于映射中")
                
            # 显示所有包含"contract"的字段
            contract_fields = [field for field in properties.keys() if "contract" in field.lower()]
            log.info(f"包含'contract'的字段: {contract_fields}")
        else:
            log.error(f"索引 {index_name} 不存在于映射中")
            
    except Exception as e:
        log.error(f"检查字段映射失败: {e}")


def main():
    """主函数"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        es = init_es_client()
        
        index_name = "markersweb_attachment_analysis_v3"
        
        log.info("=" * 60)
        log.info("调试合同包查询问题")
        log.info("=" * 60)
        
        # 1. 检查索引是否存在
        log.info("1. 检查索引状态...")
        if not check_index_exists(es, index_name):
            log.error(f"索引 {index_name} 不存在！")
            return
            
        # 2. 获取索引统计
        log.info("\n2. 获取索引统计...")
        total_docs = get_index_stats(es, index_name)
        
        # 3. 检查contract_name字段
        log.info("\n3. 检查contract_name字段...")
        contract_name_count = check_contract_name_field(es, index_name)
        
        # 4. 检查字段映射
        log.info("\n4. 检查字段映射...")
        check_field_mapping(es, index_name)
        
        # 5. 获取样本数据
        log.info("\n5. 获取contract_name样本数据...")
        sample_contract_names(es, index_name, 20)
        
        # 6. 测试不同查询方式
        log.info("\n6. 测试不同查询方式...")
        test_different_queries(es, index_name)
        
        # 7. 搜索特定模式
        log.info("\n7. 搜索特定模式...")
        search_specific_patterns(es, index_name)
        
        log.info("\n" + "=" * 60)
        log.info("调试完成")
        log.info("=" * 60)
        
    except Exception as e:
        log.error(f"调试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
