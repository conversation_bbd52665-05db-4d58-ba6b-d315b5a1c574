# Elasticsearch chn_ylcg索引文档去重脚本

## 功能概述

该脚本用于对Elasticsearch中的`chn_ylcg`索引执行文档去重操作，具体功能包括：

1. **去重字段**: 以`url`字段作为主要去重标识符
2. **优先级规则**: 
   - 当存在相同url的多个文档时，优先保留`appendix`字段不为空列表（非null且非空数组[]）的文档
   - 如果重复文档的`appendix`字段内容完全相同，则保留最后插入的文档（根据文档ID判断）
3. **安全措施**:
   - 提供`--dry-run`模式用于预览操作结果
   - 在删除前自动备份要删除的文档信息到CSV文件
   - 提供详细的操作前后对比报告
   - 记录详细的操作日志

## 使用方法

### 基本语法

```bash
python deduplicate_chn_ylcg.py [选项]
```

### 命令行参数

- `--index`: 目标索引名称（默认：chn_ylcg）
- `--batch-size`: 批处理大小（默认：100）
- `--dry-run`: 试运行模式，不执行实际删除操作

### 使用示例

#### 1. 试运行模式（推荐先执行）

```bash
# 预览去重操作，不执行实际删除
python deduplicate_chn_ylcg.py --dry-run
```

#### 2. 执行实际去重操作

```bash
# 对默认索引chn_ylcg执行去重
python deduplicate_chn_ylcg.py

# 指定索引名称
python deduplicate_chn_ylcg.py --index my_custom_index

# 指定批处理大小
python deduplicate_chn_ylcg.py --batch-size 200
```

## 去重逻辑

### 优先级规则

脚本按以下优先级选择要保留的文档：

1. **appendix字段有效性**：
   - 优先保留`appendix`字段为非空列表的文档
   - `appendix`为`null`、空数组`[]`或非数组类型的文档优先级较低

2. **文档新旧程度**：
   - 当多个文档的`appendix`字段状态相同时，保留文档ID最大的（最新插入的）文档

### 示例场景

假设有以下重复url的文档：

```
URL: "https://example.com/tender/123"
文档A: _id="doc1", appendix=null
文档B: _id="doc2", appendix=[]
文档C: _id="doc3", appendix=[{"name": "附件1"}]
```

**结果**: 保留文档C（appendix非空），删除文档A和B

```
URL: "https://example.com/tender/456"
文档D: _id="doc4", appendix=[{"name": "附件1"}]
文档E: _id="doc5", appendix=[{"name": "附件2"}]
```

**结果**: 保留文档E（文档ID更大），删除文档D

## 输出信息

### 统计信息

脚本会输出详细的统计信息：

```
去重统计:
  索引文档总数: 10000
  重复url数量: 150
  重复文档总数: 320
  计划保留文档: 150
  计划删除文档: 170
```

### 删除结果验证

```
删除结果验证:
  删除前文档数: 10000
  成功删除数: 170
  预期剩余数: 9830
  实际剩余数: 9830
  数据一致性: ✓
```

## 备份文件

脚本会自动创建备份文件，文件名格式：`deleted_documents_backup_YYYYMMDD_HHMMSS.csv`

备份文件包含以下字段：
- `doc_id`: 被删除的文档ID
- `url`: 文档的url字段
- `appendix`: 文档的appendix字段（JSON格式）
- `delete_reason`: 删除原因
- `timestamp`: 删除时间戳

## 注意事项

1. **环境配置**: 确保`.env`文件中配置了正确的Elasticsearch连接信息
2. **权限要求**: 需要对目标索引有读取和删除权限
3. **数据备份**: 虽然脚本会自动备份删除的文档信息，但建议在执行前对整个索引进行快照备份
4. **试运行**: 强烈建议先使用`--dry-run`模式预览操作结果
5. **批处理**: 对于大量数据，可以调整`--batch-size`参数优化性能

## 错误处理

脚本包含完善的错误处理机制：

- 自动检查索引是否存在
- 处理Elasticsearch连接异常
- 记录批量操作的成功和失败情况
- 支持用户中断操作（Ctrl+C）

## 日志记录

脚本使用标准的日志记录，包括：

- INFO级别：正常操作信息
- WARNING级别：警告信息（如数据不一致）
- ERROR级别：错误信息
- DEBUG级别：详细调试信息

## 依赖要求

- Python 3.7+
- elasticsearch
- python-dotenv
- 其他依赖见项目requirements.txt

## 相关文件

- `es_deal.py`: Elasticsearch操作工具模块
- `utils/log_cfg.py`: 日志配置模块
- `.env`: 环境变量配置文件
