#!/usr/bin/env python3
"""
清理合同包字段脚本

该脚本的功能：
对 contract_name 字段包含"合同包"关键词的文档进行清理，将以下4个字段全部设置为 null：
- contract_name
- contract_ext
- contract_link_out
- contract_link_key

清理规则：
- 如果 contract_name 包含"合同包"关键词，则清空所有合同相关字段
"""

import argparse
import sys
from typing import List, Dict, Optional
from dotenv import load_dotenv
import os

from es_deal import init_es_client, search_documents
from utils.log_cfg import log


# 需要清理的合同包关键词
CONTRACT_PACKAGE_KEYWORD = "合同包"

# 需要清空的合同字段
CONTRACT_FIELDS_TO_CLEAR = [
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
]


def contains_contract_package_keyword(text: str) -> bool:
    """
    检查文本是否包含"合同包"关键词

    Args:
        text (str): 要检查的文本

    Returns:
        bool: 如果包含"合同包"关键词则返回True，否则返回False
    """
    if not text:
        return False

    return CONTRACT_PACKAGE_KEYWORD in text


def get_documents_with_contract_package(
    es_client, index_name: str, batch_size: int = 100
) -> List[Dict]:
    """
    获取包含"合同包"关键词的文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小

    Returns:
        包含"合同包"关键词的文档列表
    """
    try:
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "contract_name"}},
                        {"match_phrase": {"contract_name": CONTRACT_PACKAGE_KEYWORD}},
                    ]
                }
            },
            "_source": CONTRACT_FIELDS_TO_CLEAR,
            "size": batch_size,
        }

        result = search_documents(es_client, index_name, query=query)
        hits = result.get("hits", {}).get("hits", [])

        documents = []
        for hit in hits:
            source = hit["_source"]
            doc_info = {
                "doc_id": hit["_id"],
                "contract_name": source.get("contract_name"),
                "contract_ext": source.get("contract_ext"),
                "contract_link_out": source.get("contract_link_out"),
                "contract_link_key": source.get("contract_link_key"),
            }
            documents.append(doc_info)

        return documents

    except Exception as e:
        log.error(f"获取包含合同包关键词的文档失败: {e}")
        return []


def get_total_contract_package_count(es_client, index_name: str) -> int:
    """
    获取包含"合同包"关键词的文档总数

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称

    Returns:
        文档总数
    """
    try:
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "contract_name"}},
                        {"match_phrase": {"contract_name": CONTRACT_PACKAGE_KEYWORD}},
                    ]
                }
            }
        }

        result = es_client.count(index=index_name, body=query)
        return result.get("count", 0)

    except Exception as e:
        log.error(f"获取文档总数失败: {e}")
        return 0


def bulk_update_documents(es_client, index_name: str, updates: List[Dict]):
    """
    批量更新文档字段

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        updates: 更新操作列表
    """
    if not updates:
        return {"success": 0, "failed": 0}

    try:
        # 构建批量更新请求
        body = []
        for update in updates:
            # 更新操作头
            body.append({"update": {"_index": index_name, "_id": update["doc_id"]}})
            # 更新内容
            body.append({"doc": update["fields"]})

        # 执行批量更新
        response = es_client.bulk(body=body)

        # 统计结果
        success_count = 0
        failed_count = 0

        for item in response.get("items", []):
            if "update" in item:
                if item["update"].get("status") in [200, 201]:
                    success_count += 1
                else:
                    failed_count += 1
                    log.error(f"更新失败: {item['update']}")

        log.info(f"批量更新完成 - 成功: {success_count}, 失败: {failed_count}")
        return {"success": success_count, "failed": failed_count}

    except Exception as e:
        log.error(f"批量更新文档失败: {e}")
        return {"success": 0, "failed": len(updates)}


def process_contract_package_cleanup(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = True
):
    """
    处理合同包字段清理

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info(f"开始处理合同包字段清理...")
        log.info(f"索引: {index_name}")
        log.info(f"批处理大小: {batch_size}")
        log.info(f"试运行模式: {dry_run}")

        # 先获取总数统计
        total_docs = get_total_contract_package_count(es_client, index_name)
        log.info(
            f"索引中包含'{CONTRACT_PACKAGE_KEYWORD}'关键词的文档总数: {total_docs}"
        )

        if total_docs == 0:
            log.info("没有找到包含'合同包'关键词的文档")
            return

        # 获取需要清理的文档
        documents = get_documents_with_contract_package(
            es_client, index_name, batch_size
        )

        if not documents:
            log.info("没有找到需要清理的文档")
            return

        log.info(f"本批次检查 {len(documents)} 个文档（总共 {total_docs} 个）")

        # 分析需要清理的文档
        updates = []
        cleaned_count = 0

        for i, doc in enumerate(documents, 1):
            doc_id = doc["doc_id"]
            contract_name = doc["contract_name"]

            # 检查是否包含"合同包"关键词
            if contains_contract_package_keyword(contract_name):
                # 准备清空所有合同字段
                update_fields = {}
                for field in CONTRACT_FIELDS_TO_CLEAR:
                    update_fields[field] = None

                updates.append({"doc_id": doc_id, "fields": update_fields})

                cleaned_count += 1

                # 输出修改前后对比
                log.info(f"[{i}/{len(documents)}] 清理合同包字段 {doc_id}:")
                log.info(f"  修改前 - contract_name: '{contract_name}'")
                log.info(f"  修改前 - contract_ext: '{doc.get('contract_ext')}'")
                log.info(
                    f"  修改前 - contract_link_out: '{doc.get('contract_link_out')}'"
                )
                log.info(
                    f"  修改前 - contract_link_key: '{doc.get('contract_link_key')}'"
                )
                log.info(f"  修改后 - 所有合同字段: null")

        # 输出统计信息
        log.info("=" * 50)
        log.info("处理统计:")
        log.info(f"  本批次文档总数: {len(documents)}")
        log.info(f"  需要清理的文档: {cleaned_count}")
        log.info(f"  清理字段: {CONTRACT_FIELDS_TO_CLEAR}")
        log.info(f"  清理原因: 包含'{CONTRACT_PACKAGE_KEYWORD}'关键词")
        log.info("=" * 50)

        if updates and not dry_run:
            # 执行批量更新
            result = bulk_update_documents(es_client, index_name, updates)
            log.info(f"清理完成 - 成功: {result['success']}, 失败: {result['failed']}")
        elif dry_run:
            log.info("试运行模式，未执行实际更新")
        else:
            log.info("没有需要清理的文档")

    except Exception as e:
        log.error(f"处理过程中发生错误: {e}")


def process_all_contract_package_cleanup(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = True
):
    """
    处理所有包含"合同包"关键词的文档清理

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info("开始处理所有需要清理的合同包文档...")

        # 获取总数
        total_docs = get_total_contract_package_count(es_client, index_name)
        log.info(
            f"索引中包含'{CONTRACT_PACKAGE_KEYWORD}'关键词的文档总数: {total_docs}"
        )

        if total_docs == 0:
            log.info("没有找到需要清理的文档")
            return

        processed_count = 0
        batch_num = 1
        total_cleaned = 0
        total_updated = 0

        # 使用scroll API处理大量数据
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "contract_name"}},
                        {"match_phrase": {"contract_name": CONTRACT_PACKAGE_KEYWORD}},
                    ]
                }
            },
            "_source": CONTRACT_FIELDS_TO_CLEAR,
        }

        # 初始化scroll
        scroll_response = es_client.search(
            index=index_name, body=query, scroll="5m", size=batch_size
        )
        scroll_id = scroll_response["_scroll_id"]
        hits = scroll_response.get("hits", {}).get("hits", [])

        while hits:
            log.info(f"\n{'='*60}")
            log.info(f"开始处理第 {batch_num} 批次")
            log.info(f"已处理: {processed_count}/{total_docs}")
            log.info(f"{'='*60}")

            # 处理当前批次
            documents = []
            for hit in hits:
                source = hit["_source"]
                doc_info = {
                    "doc_id": hit["_id"],
                    "contract_name": source.get("contract_name"),
                    "contract_ext": source.get("contract_ext"),
                    "contract_link_out": source.get("contract_link_out"),
                    "contract_link_key": source.get("contract_link_key"),
                }
                documents.append(doc_info)

            # 处理当前批次的清理
            batch_stats = process_batch_contract_package_cleanup(
                es_client, index_name, documents, dry_run
            )

            total_cleaned += batch_stats["cleaned"]
            total_updated += batch_stats["updated"]
            processed_count += len(documents)
            batch_num += 1

            # 获取下一批数据
            try:
                scroll_response = es_client.scroll(scroll_id=scroll_id, scroll="5m")
                hits = scroll_response.get("hits", {}).get("hits", [])
            except Exception as e:
                log.error(f"Scroll查询失败: {e}")
                break

        # 清理scroll
        try:
            es_client.clear_scroll(scroll_id=scroll_id)
        except Exception as e:
            log.warning(f"清理scroll失败: {e}")

        # 输出最终统计
        log.info(f"\n{'='*60}")
        log.info("最终统计:")
        log.info(f"  处理的文档总数: {processed_count}")
        log.info(f"  需要清理的文档: {total_cleaned}")
        log.info(f"  成功更新的文档: {total_updated}")
        log.info(f"  清理字段: {CONTRACT_FIELDS_TO_CLEAR}")
        log.info(f"  清理原因: 包含'{CONTRACT_PACKAGE_KEYWORD}'关键词")
        log.info(f"{'='*60}")

    except Exception as e:
        log.error(f"处理过程中发生错误: {e}")


def process_batch_contract_package_cleanup(
    es_client, index_name: str, documents: List[Dict], dry_run: bool = True
) -> Dict:
    """
    处理单个批次的合同包文档清理

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        documents: 文档列表
        dry_run: 是否为试运行模式

    Returns:
        处理统计信息
    """
    updates = []
    cleaned_count = 0

    for i, doc in enumerate(documents, 1):
        doc_id = doc["doc_id"]
        contract_name = doc["contract_name"]

        # 检查是否包含"合同包"关键词
        if contains_contract_package_keyword(contract_name):
            # 准备清空所有合同字段
            update_fields = {}
            for field in CONTRACT_FIELDS_TO_CLEAR:
                update_fields[field] = None

            updates.append({"doc_id": doc_id, "fields": update_fields})

            cleaned_count += 1
            log.info(
                f"[{i}/{len(documents)}] 清理合同包字段 {doc_id}: '{contract_name}'"
            )

    # 执行更新
    update_result = {"success": 0, "failed": 0}
    if updates and not dry_run:
        update_result = bulk_update_documents(es_client, index_name, updates)
    elif dry_run and updates:
        log.info(f"试运行模式: 将清理 {len(updates)} 个文档")

    return {
        "cleaned": cleaned_count,
        "updated": update_result["success"],
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="清理合同包字段：将包含'合同包'关键词的合同文件字段设置为null"
    )
    parser.add_argument(
        "--index",
        type=str,
        default="markersweb_attachment_analysis_v3",
        help="目标索引名称（默认: markersweb_attachment_analysis_v3）",
    )
    parser.add_argument(
        "--batch-size", type=int, default=100, help="批处理大小（默认100）"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        default=True,
        help="试运行模式，不执行实际更新（默认启用）",
    )
    parser.add_argument(
        "--execute", action="store_true", help="执行实际更新操作（禁用dry-run模式）"
    )
    parser.add_argument(
        "--all", action="store_true", help="处理所有需要清理的文档（分批处理）"
    )

    args = parser.parse_args()

    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        es = init_es_client()

        # 确定dry_run模式
        dry_run = not args.execute  # 如果指定了--execute，则禁用dry_run

        index_name = args.index

        log.info("=" * 60)
        log.info("合同包字段清理脚本")
        log.info("=" * 60)

        log.info(f"目标索引: {index_name}")
        log.info(f"批处理大小: {args.batch_size}")
        log.info(f"试运行模式: {dry_run}")
        log.info(f"处理所有文档: {args.all}")

        # 输出清理规则
        log.info("=" * 60)
        log.info("清理规则:")
        log.info(f"  目标关键词: '{CONTRACT_PACKAGE_KEYWORD}'")
        log.info(f"  清理字段: {CONTRACT_FIELDS_TO_CLEAR}")
        log.info("  如果contract_name包含'合同包'关键词，将清空所有合同相关字段")
        log.info("=" * 60)

        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            log.error(f"索引 {index_name} 不存在")
            sys.exit(1)

        # 安全确认
        if not dry_run:
            total_docs = get_total_contract_package_count(es, index_name)
            log.warning(f"即将修改 {total_docs} 个文档的合同字段！")
            confirm = input("确认执行实际修改操作？(输入 'YES' 确认): ")
            if confirm != "YES":
                log.info("操作已取消")
                sys.exit(0)

        # 处理文档清理
        if args.all:
            process_all_contract_package_cleanup(
                es, index_name, args.batch_size, dry_run
            )
        else:
            process_contract_package_cleanup(es, index_name, args.batch_size, dry_run)

    except KeyboardInterrupt:
        log.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        log.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
